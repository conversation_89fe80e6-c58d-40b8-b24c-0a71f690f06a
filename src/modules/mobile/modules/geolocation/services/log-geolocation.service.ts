import { Injectable, NotFoundException } from '@nestjs/common';
import { LogIntervalGeolocationDto } from '../dto/log-interval-geolocation.dto';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { DataSource, Repository } from 'typeorm';
import { LogGeolocation } from '../../../../../common/modules/database/entities/log-geolocation.entity';
import { v4 as uuidv4 } from 'uuid';
import { LogAlert } from '../../../../../common/modules/database/entities/log-alert.entity';
import { Geofence } from '../../../../../common/modules/database/entities/geofence.entity';
import { isPointInGeoJSONPolygon } from '../../../../../common/utils/geo.utils';
import { DeviceHeader } from '../../../../../common/decorators/current-device.decorator';
import { Device } from '../../../../../common/modules/database/entities/device.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class LogGeolocationService {
  constructor(
    private readonly dataSource: DataSource,
    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,
  ) {}

  async submitInterval(
    geolocationDto: LogIntervalGeolocationDto,
    user: User,
    deviceFromHeader: DeviceHeader,
  ) {
    const getGeofence = await this.dataSource
      .getRepository(Geofence)
      .createQueryBuilder('geofence')
      .leftJoinAndSelect('geofence.branch', 'branch')
      .leftJoinAndSelect('geofence.zone', 'zone')
      .where('geofence.parent_branch_id = :parentBranchId', {
        parentBranchId: user.parent_branch_id,
      })
      .getMany();

    let selectedGeofence: Geofence | null = null;
    if (getGeofence.length > 0) {
      for (const geofence of getGeofence) {
        const geofenceData = geofence.geofence_data;
        if (geofenceData) {
          const isInside = isPointInGeoJSONPolygon(
            {
              latitude: geolocationDto.latitude,
              longitude: geolocationDto.longitude,
            },
            geofenceData,
          );
          if (isInside) {
            selectedGeofence = geofence;
            break;
          }
        }
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let device: Device | null = null;
      // Validate Device
      if (deviceFromHeader.device_uid) {
        const getDevice = await this.deviceRepository.findOne({
          where: {
            imei: deviceFromHeader.device_uid,
          },
        });
        if (!getDevice) {
          throw new NotFoundException('Device not found');
        }
        device = getDevice;
      }

      const logGeolocation = new LogGeolocation();
      logGeolocation.uuid = uuidv4();
      logGeolocation.parent_branch_id = user.parent_branch_id;
      if (selectedGeofence) {
        logGeolocation.branch_id = selectedGeofence.branch_id;
        logGeolocation.branch_name = selectedGeofence.branch.branch_name;
        logGeolocation.geofence_id = selectedGeofence.id;
        logGeolocation.geofence_name = selectedGeofence.geofence_name;
        logGeolocation.zone_id = selectedGeofence.zone_id;
        logGeolocation.zone_name = selectedGeofence.zone.zone_name;

        logGeolocation.active_time_start = selectedGeofence.active_time_start;
        logGeolocation.active_time_end = selectedGeofence.active_time_end;
        logGeolocation.minimum_stay_duration =
          selectedGeofence.minimum_stay_duration;
        logGeolocation.maximum_stay_duration =
          selectedGeofence.maximum_stay_duration;
      } else {
        logGeolocation.branch_id = user.parent_branch_id;
        logGeolocation.branch_name = user.parent_branch.branch_name;
      }
      logGeolocation.role_id = user.role_id;
      logGeolocation.role_name = user.role.role_name;
      logGeolocation.user_id = user.id;
      logGeolocation.user_name = user.name;
      logGeolocation.latitude = geolocationDto.latitude;
      logGeolocation.longitude = geolocationDto.longitude;
      if (device) {
        logGeolocation.device_id = device.id;
        logGeolocation.device_name = device.device_name;
      }
      if(selectedGeofence){
        logGeolocation.timezone_id = selectedGeofence.branch.timezone.id;
        logGeolocation.timezone_name = selectedGeofence.branch.timezone.timezone_name;
      } else {
        logGeolocation.timezone_id = user.parent_branch.timezone.id;
        logGeolocation.timezone_name = user.parent_branch.timezone.timezone_name;
      }
      logGeolocation.event_time = new Date();
      logGeolocation.original_submitted_time = new Date(
        geolocationDto.original_submitted_time,
      );
      logGeolocation.geofence_data = {
        type: 'Point',
        coordinates: [geolocationDto.longitude, geolocationDto.latitude],
      };

      // Save logGeolocation using queryRunner
      const savedLogGeolocation = await queryRunner.manager.save(
        LogGeolocation,
        logGeolocation,
      );
      if (selectedGeofence) {
        const logAlert = new LogAlert();
        logAlert.uuid = uuidv4();
        logAlert.alert_event_id = 8;
        logAlert.alert_event_name = 'Geofences';
        logAlert.log_id = savedLogGeolocation.id;
        logAlert.log_uuid = savedLogGeolocation.uuid;
        logAlert.reference_name = 'Location';
        logAlert.parent_branch_id = user.parent_branch_id;
        logAlert.user_id = user.id;
        logAlert.role_id = user.role_id;
        logAlert.payload_data = {
          type: 'geofence',
          location: geolocationDto,
          logGeolocation: savedLogGeolocation,
        };
        logAlert.deleted_on_dashboard = false;
        logAlert.original_submitted_time =
          savedLogGeolocation.original_submitted_time;
        logAlert.event_time = savedLogGeolocation.event_time;

        // Save logAlert using queryRunner
        await queryRunner.manager.save(LogAlert, logAlert);
      }

      await queryRunner.commitTransaction();

      return {
        message: 'Location processed successfully',
        data: geolocationDto,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
